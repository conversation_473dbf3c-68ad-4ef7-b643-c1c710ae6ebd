/* Hubble Agent 页面样式 */
.hubble-page__content {
  display: flex;
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  background: #f8f8f8;
}

.hubble-page__content-document {
  flex: 1;
  min-width: 0;
  border-right: 1px solid #eee;
}

.hubble-page__content-chat {
  width: 400px;
  min-width: 400px;
  background: #fff;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hubble-page__content {
    flex-direction: column;
  }
  
  .hubble-page__content-document {
    flex: 1;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
  
  .hubble-page__content-chat {
    width: 100%;
    min-width: auto;
    height: 50vh;
  }
}

@media (max-width: 768px) {
  .hubble-page__content-chat {
    height: 40vh;
  }
}

/* 重置默认样式，确保全屏显示 */
body.hubble-fullscreen {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app.hubble-fullscreen {
  max-width: none;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}
