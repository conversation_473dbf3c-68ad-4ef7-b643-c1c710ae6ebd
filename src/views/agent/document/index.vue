<template>
    <div class="hubble-document">
        <div class="hubble-document__header">
            <div class="hubble-document__title">
                {{ documentInfo.fileName }}
            </div>
            <div class="hubble-document__scale-box">
                <i class="iconfont el-icon-ssq-suoxiaojing scale" @click="handleScale('minus')"></i>
                <i class="iconfont el-icon-ssq-fangdajing1 scale" @click="handleScale('plus')"></i>
                <i class="el-icon-ssq-jiantou1" @click="handlePage('pre')"></i>
                <el-input v-model="currentPage" @keyup.enter.native="scrollTo" @blur="scrollTo"></el-input>
                <span><i>/</i>  <em>{{ documentInfo.pageSize }}</em></span>
                <i class="el-icon-ssq-jiantou1" @click="handlePage('next')"></i>
            </div>
        </div>
        <el-tooltip :open-delay="500" effect="dark" content="选中文字后点击提问" placement="top">
            <div :id="showQuote ? '' : 'guide-quote'" class="hubble-document__quote fixed" v-show="!showQuote"></div>
        </el-tooltip>
        <div class="hubble-document__content" ref="documentContent" @scroll="handleScroll">
            <div class="hubble-document__content-loading" v-if="pdfLoading">
                <img src="~img/loading.gif" alt="">
            </div>
            <div class="hubble-document__content-box" ref="pdfContainer">
                <el-tooltip :open-delay="500" effect="dark" content="选中文字后点击提问" placement="top">
                    <div :id="!showQuote ? '' : 'guide-quote'" class="hubble-document__quote" v-show="showQuote" :style="quoteStyle" @click.stop="handleQuote"></div>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import * as PDFJS from 'pdfjs-dist/build/pdf.js';
import  workerSrc from 'pdfjs-dist/build/pdf.worker.entry.js';
import 'pdfjs-dist/web/pdf_viewer.css';
import 'pdfjs-dist/web/pdf_viewer.js';
import { useHubbleStore } from '@/stores/hubble'
import { throttle } from 'utils/fn.js'

PDFJS.workerSrc = workerSrc;
PDFJS.disableWorker = true
PDFJS.cMapPacked = true

// Props
const props = defineProps({
    topicId: {
        type: String,
        default: '',
    },
})

// Store
const hubbleStore = useHubbleStore()
const route = useRoute()

// Reactive data
const DPR = ref(2)
const maxWidth = ref(0)
const preContent = ref('')
const preCoordinate = ref({})
const textLayer = ref(null)
const showQuote = ref(false)
const quoteTop = ref(null)
const quoteLeft = ref(null)
const selectedArea = ref(null)
const currentPage = ref(1)
const pageHeights = ref([])
const scale = ref(1)
const selectedIndex = ref(0)
const scrollDisabled = ref(false)
const pdfLoading = ref(true)

// Refs
const documentContent = ref(null)
const pdfContainer = ref(null)

// Computed properties
const pdfUrl = computed(() => {
    // 接口调用逻辑已移除，返回模拟URL
    return `/web/hubble/topic/${props.topicId}/documents/${hubbleStore.documentInfo.documentId}/download`
})

const quoteStyle = computed(() => {
    return {
        '--quote-top': quoteTop.value,
        '--quote-left': quoteLeft.value,
    }
})
// Watchers
watch(scale, () => {
    setTimeout(() => {
        if (pdfContainer.value) {
            pdfContainer.value.style.width = `${maxWidth.value * scale.value}px`
        }
    }, 1000)
})

watch(() => hubbleStore.startSelectQuote, (val) => {
    if (val) {
        clearSelectedArea()
        const parent = document.querySelector(`#page-${hubbleStore.quotePage}`)
        if (parent) {
            const { offsetHeight, offsetWidth, offsetTop } = parent
            const { x, y, width, height } = hubbleStore.quoteCoordinate
            const realLeft = x * offsetWidth
            const realTop = offsetHeight * y
            const realWidth = width * offsetWidth
            const realHeight = height * offsetHeight
            const currentQuoteArea = parent.querySelector('.hubble-document__selected')
            if (currentQuoteArea) {
                currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`
            }
            if (documentContent.value) {
                documentContent.value.scrollTo({
                    top: realTop + offsetTop - 100,
                    behavior: 'smooth',
                })
            }
            hubbleStore.setStartSelectQuote(false)
        }
    }
})
// Methods
const init = async () => {
    await getDocumentInfo()
    handlePDF()
    if (documentContent.value) {
        documentContent.value.addEventListener('mouseup', () => {
            const sel = window.getSelection()
            const text = sel.toString()
            clearSelectedArea()
            if (text) {
                preContent.value = text
                showQuote.value = true
                handleSelectedNode()
            }
        })
    }
}

const handlePDF = () => {
    PDFJS.getDocument(pdfUrl.value).then((pdf) => {
        pageHeights.value = []
        for (var i = 1; i <= pdf.numPages; i++) {
            renderPDF(pdf, i)
        }
    })
}

const getDocumentInfo = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    return new Promise((resolve) => {
        // 模拟接口调用成功后的回调
        setTimeout(() => {
            const mockDocumentInfo = {
                documentId: 'mock-doc-id',
                fileName: '示例文档.pdf',
                pageSize: 10,
                documentPreview: {
                    maxWidth: 800
                }
            }
            hubbleStore.setDocumentInfo(mockDocumentInfo)
            maxWidth.value = mockDocumentInfo.documentPreview?.maxWidth || 800
            if (documentContent.value) {
                const scaleValue = (documentContent.value.offsetWidth - 40) / maxWidth.value
                scale.value = scaleValue < 1 ? 1 : (scaleValue > 2 ? 2 : scaleValue)
            }
            resolve()
        }, 500)
    })
}
const renderPDF = async (pdf, num) => {
    const page = await pdf.getPage(num)
    const viewport = page.getViewport(scale.value * DPR.value)
    const pageDiv = document.querySelector(`#page-${page.pageIndex + 1}`) || (() => {
        const newPageDiv = document.createElement('div')
        newPageDiv.setAttribute('id', `page-${page.pageIndex + 1}`)
        newPageDiv.setAttribute('class', 'document-page')
        newPageDiv.setAttribute('style', 'position: relative')
        if (pdfContainer.value) {
            pdfContainer.value.appendChild(newPageDiv)
        }
        const quoteDiv = document.createElement('div')
        quoteDiv.setAttribute('class', 'hubble-document__selected border-scroll')
        quoteDiv.setAttribute('style', 'display: none')
        newPageDiv.appendChild(quoteDiv)
        const canvas = document.createElement('canvas')
        newPageDiv.appendChild(canvas)
        return newPageDiv
    })()

    const canvas = pageDiv.querySelector('canvas')
    const context = canvas.getContext('2d')
    canvas.height = viewport.height
    canvas.width = viewport.width
    pageDiv.style.height = `${viewport.height / DPR.value}px`
    canvas.style.height = `${viewport.height / DPR.value}px`
    canvas.style.width = `${viewport.width / DPR.value}px`
    pageHeights.value.push(viewport.height / DPR.value)

    const renderContext = {
        canvasContext: context,
        viewport,
    }

    page.render(renderContext).then(() => {
        pdfLoading.value && (pdfLoading.value = false)
        return page.getTextContent()
    }).then((textContent) => {
        const textLayerDiv = pageDiv.querySelector('.textLayer') || (() => {
            const newDiv = document.createElement('div')
            pageDiv.appendChild(newDiv)
            newDiv.addEventListener('mouseup', () => {
                selectedIndex.value = num
            })
            return newDiv
        })()

        const textLayerDivWidth = canvas.style.width
        const textLayerDivHeight = canvas.style.height
        textLayerDiv.setAttribute('class', 'textLayer')
        textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`)

        if (textLayerDiv) {
            textLayerDiv.innerHTML = ''
            PDFJS.renderTextLayer({
                textContent,
                container: textLayerDiv,
                viewport: page.getViewport(scale.value),
            })
        }
    })
}
const handleSelectedNode = () => {
    const sel = window.getSelection()
    // 获取选中元素
    const range = sel.getRangeAt(0)
    const container = range.commonAncestorContainer
    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT)
    const selectedNodes = []

    while (walker.nextNode()) {
        const node = walker.currentNode
        if (range.intersectsNode(node) && !node.className && node.nodeName !== 'CANVAS') {
            selectedNodes.push(node)
        }
    }

    // 确定选中元素范围
    let minLeft = null
    let maxLeft = null
    let minTop = null
    let maxTop = null
    let startNode = null

    selectedNodes.forEach((node) => {
        const { offsetLeft, offsetWidth, offsetHeight } = node
        const { top } = node.getBoundingClientRect()
        const realMaxLeft = offsetLeft + offsetWidth
        const realMaxTop = top + offsetHeight

        if (!minLeft || offsetLeft < minLeft) {
            minLeft = offsetLeft
        }
        if (!maxLeft || realMaxLeft > maxLeft) {
            maxLeft = realMaxLeft
        }
        if (!minTop || top < minTop) {
            minTop = top
            startNode = node
        }
        if (!maxTop || realMaxTop > maxTop) {
            maxTop = realMaxTop
        }
    })

    // 给选中范围添加边框
    if (startNode && documentContent.value) {
        const parent = startNode.parentNode.parentNode
        const realTop = documentContent.value.scrollTop + minTop - parent.offsetTop - 120 - 20
        const realLeft = minLeft
        const realWidth = maxLeft - minLeft
        const realHeight = maxTop - minTop + 4
        const currentQuoteArea = parent.querySelector('.hubble-document__selected')

        if (currentQuoteArea) {
            currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`
        }

        quoteTop.value = `${realTop + maxTop - minTop - 25 + parent.offsetTop}px`
        quoteLeft.value = `${realLeft + realWidth}px`
        preCoordinate.value = {
            x: realLeft / parent.offsetWidth,
            y: realTop / parent.offsetHeight,
            width: realWidth / parent.offsetWidth,
            height: realHeight / parent.offsetHeight,
        }
    }
}

const handleQuote = () => {
    hubbleStore.setQuote({
        content: preContent.value,
        documentId: hubbleStore.documentInfo.documentId,
        quoteCoordinate: preCoordinate.value,
        pageNumber: selectedIndex.value,
        quoteFormat: 'TEXT',
    })
    preContent.value = ''
}

const clearSelectedArea = () => {
    showQuote.value = false
    quoteTop.value = null
    quoteLeft.value = null
    // currentQuoteArea 需要在使用时获取
}
const handleScroll = throttle((e) => {
    if (scrollDisabled.value) {
        return
    }
    let height = 0
    pageHeights.value.some((el, i) => {
        height += el + 20
        if (height > e.target.scrollTop) {
            currentPage.value = i + 1
            return true
        }
    })
}, 50)

const scrollTo = () => {
    let top = 0
    for (let i = 0; i < currentPage.value - 1; i++) {
        top += pageHeights.value[i] + 20
    }
    top += 10
    scrollDisabled.value = true

    if (documentContent.value) {
        documentContent.value.scrollTo({
            top: top,
            behavior: 'smooth',
        })
    }

    setTimeout(() => {
        scrollDisabled.value = false
    }, 1000)
}

const handlePage = (type) => {
    if (type === 'pre') {
        currentPage.value = currentPage.value - 1 < 1 ? 1 : currentPage.value - 1
    } else {
        currentPage.value = currentPage.value + 1 > hubbleStore.documentInfo.pageSize ? hubbleStore.documentInfo.pageSize : currentPage.value + 1
    }
    scrollTo()
}

const handleScale = (type) => {
    clearSelectedArea()
    if (type === 'plus') {
        if (scale.value === 2) {
            return
        }
        scale.value = scale.value + 0.2 > 2 ? 2 : scale.value + 0.2
    } else {
        if (scale.value === 0.5) {
            return
        }
        scale.value = scale.value - 0.2 < 0.5 ? 0.5 : scale.value - 0.2
    }
    handlePDF()
}

// 生命周期钩子
onMounted(() => {
    init()
})
</script>
<style lang="scss">
.hubble-document{
    display: flex;
    flex-direction: column;
    position: relative;
    &__header{
        height: 55px;
        line-height: 55px;
        padding: 0 20px;
        background: #FFFFFF;
        box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        position: relative;
        z-index: 9;
    }
    &__title i{
        margin-right: 20px;
        cursor: pointer;
        &.active{
            color: #409EFF;
        }
    }
    &__scale-box{
        position: absolute;
        height: 100%;
        padding: 0 10px;
        top: 0;
        right: 0;
        user-select: none;
        background: #fff;
        i{
            cursor: pointer;
            margin: 0 15px;
            &.el-icon-ssq-jiantou1:nth-child(3){
                transform: rotate(180deg);
            }
        }
        .el-input{
            display: inline-block;
            width: 44px;
            input{
                height: 24px;
                position: relative;
                top: -2px;
                text-align: center;
            }
        }
        span{
            display: inline-block;
            position: relative;
            padding-left: 13px;
            font-size: 18px;
            i{
                position: absolute;
                left: -10px;
                top: -1px;
            }
            em{
                font-weight: normal;
            }
        }
    }
    &__content{
        flex: 1;
        background: #f8f8f8;
        position: relative;
        overflow: auto;
        ::selection {
            background: #0079FE;
        }
        &-loading{
            width: calc(100% - 40px);
            height: 100vh;
            margin: 20px auto;
            background: #fff;
            img{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
        &-box{
            overflow: auto;
            overflow-x: hidden;
            margin: 20px auto 0;
            background: #f8f8f8;
            position: relative;
            canvas{
                position: absolute;
                top: 0;
                left: 0;
            }
            .textLayer{
                top: 0;
                left: 0;
            }
            .document-page{
                background: #fff;
                margin-bottom: 20px;
            }
        }
    }
    &__quote{
        position: absolute;
        top: var(--quote-top, calc(100% - 70px));
        left: var(--quote-left, calc(100% - 50px));
        z-index: 9;
        right: 20px;
        width: 49px;
        height: 44px;
        background: transparent;
        transition: all .5s;
        background-image: url(~img/quoteIcon.png);
        background-size: cover;
        cursor: pointer;
        &:hover{
            background-image: url(~img/quoteIconHover.png);
        }
        &.fixed{
            top: calc(100% - 56px);
            left: calc(100% - 56px);
        }
    }

    @keyframes blink {
        0% {
            border-color: transparent;
        }
        50% {
            border-color: #0C8AEE;
        }
        100% {
            border-color: transparent;
        }
    }
    .border-scroll {
        position: absolute;
        z-index: 9;
        border: 2px dashed #0C8AEE;
        animation: blink 1s infinite;
    }
}
</style>
