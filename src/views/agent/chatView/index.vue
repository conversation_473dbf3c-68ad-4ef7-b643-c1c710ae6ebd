<template>
    <div ref="chat" class="hubble-chat">
        <div class="hubble-chat__header">
            <span>
                <img src="~img/AIBot.png" class="avatar" alt="">
                Hubble
            </span>
            <div class="operate">
                <el-popover
                    trigger="click"
                    popper-class="download-popover"
                    v-model="languageSwitchVisible"
                >
                    <el-tooltip slot="reference" :open-delay="500" effect="dark" content="输出语言切换" placement="top">
                        <i class="el-icon-ssq-diqiu" id="guide-lang"></i>
                    </el-tooltip>
                    <ul>
                        <li :class="isEn ? '' : 'active'" @click="switchEnLanguage(false)">中文语言</li>
                        <li :class="isEn ? 'active' : ''" @click="switchEnLanguage(true)">英文语言</li>
                    </ul>
                </el-popover>
                <el-popover
                    trigger="click"
                    popper-class="download-popover"
                >
                    <el-tooltip slot="reference" :open-delay="500" effect="dark" content="文档下载" placement="top">
                        <i class="el-icon-ssq--bs-xiazai"></i>
                    </el-tooltip>
                    <ul>
                        <li @click="handleDownloadChat(false)">源文件</li>
                        <li @click="handleDownloadChat">源文件 (携带对话)</li>
                    </ul>
                </el-popover>
            </div>
        </div>
        <div class="hubble-chat__body" @scroll="handleScroll">
            <div class="hubble-chat__loading" v-if="chatInitLoading">
                <img src="~img/loading2.gif" alt=""><br>
                <span>文档解析中</span>
            </div>
            <template v-else>
                <ChatList
                    :messages="messages"
                    :typing="typing"
                    @selectQuote="handleSelectQuote"
                    @showContinuousChat="handleShowContinuousChat"
                    @loadMore="handleScroll"
                ></ChatList>
                <transition name="fade">
                    <div v-show="showContinuousChat" class="continuous-chat">
                        <ChatList
                            isContinuousChat
                            :messages="continuousMessages"
                            :typing="typing"
                            @selectQuote="handleSelectQuote"
                            @loadMore="handleLoadMoreContinuousChat"
                        ></ChatList>
                        <div class="continuous-exit">
                            <span>连续对话模式：</span>
                            <span class="exit" @click="showContinuousChat = false">退出</span>
                        </div>
                    </div>
                </transition>
            </template>
        </div>
        <div class="hubble-chat__dialog">
            <transition name="slide">
                <div class="hubble-chat__quote" v-show="!!preQuote.content">
                    <div class="hubble-chat__quote-header">
                        <span>引用内容：</span>
                        <i class="el-icon-ssq-guanbi" @click="clearQuote"></i>
                    </div>
                    <div class="hubble-chat__quote-body chat-quote" @click="handleSelectQuote(preQuote)">{{ preQuote.content }}</div>
                </div>
            </transition>
            <transition name="slide">
                <div :class="`hubble-chat__suggestion ${!preQuote.content ? 'without-quote' : ''}`" v-show="showSuggestion">
                    <div class="hubble-chat__suggestion-header">
                        <i class="el-icon-ssq-gaoliangtishiH5"></i>
                        建议问题：
                        <i class="el-icon-ssq-guanbi" @click="showSuggestion = false"></i>
                    </div>
                    <ul class="hubble-chat__suggestion-list">
                        <li
                            class="hubble-chat__suggestion-item"
                            v-for="(suggestion, i) in currentSuggestions"
                            :key="i"
                            @click="handleSendSuggestion(suggestion)"
                        >
                            <span>{{ suggestion.suggestQuestionContent }}</span>
                            <i class="el-icon-ssq-fajianxiang"></i>
                        </li>
                        <li class="hubble-chat__suggestion-item empty" v-if="!currentSuggestions.length">
                            <img src="~img/loading.gif" v-if="loadSuggestion" alt="">
                            <span v-else>没有数据</span>
                        </li>
                    </ul>
                </div>
            </transition>
        </div>
        <div class="hubble-chat__footer" v-if="!chatInitLoading">
            <div :class="`hubble-chat__input ${currentPrompt.displayName ? 'line-feed' : ''}`" @keydown="handleKeyDown">
                <ul class="hubble-chat__prompt" v-show="showPrompt">
                    <li
                        v-for="(plugin, index) in filterPromptList"
                        :class="`hubble-chat__prompt-plugin ${ index === currentPromptIndex ? 'active' : '' }`"
                        @click="handleSelectPlugin(plugin)"
                        :key="plugin.pluginName"
                    >
                        <i :class="plugin.icon" v-if="plugin.icon"></i>
                        <div>
                            <p>
                                {{ plugin.displayName }}
                                <span>（{{ plugin.shortName }}）</span>
                            </p>
                            <span>{{ plugin.desc }}</span>
                        </div>
                    </li>
                </ul>
                <span ref="inputPlugin" class="hubble-chat__input-plugin" v-show="!!currentPrompt.displayName">/ {{ currentPrompt.displayName }}</span>
                <el-input
                    id="guide-text"
                    ref="inputItem"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 6}"
                    resize="none"
                    placeholder="请输入您的问题"
                    v-model="input"
                    :disabled="btnDisabled"
                    @blur="inputBlur"
                    @focus="inputActive = true"
                ></el-input>
            </div>
            <div class="hubble-chat__footer-operate">
                <el-tooltip :open-delay="500" effect="dark" content="建议问题" placement="top">
                    <i id="guide-suggestion" class="operate-icon el-icon-ssq-Hubbletishi" @click="handleShowSuggestion"></i>
                </el-tooltip>
                <el-button type="primary" :disabled="btnDisabled" @click="handleSendMessage">发送</el-button>
            </div>
        </div>
        <i ref="move" class="hubble-chat__move el-icon-ssq-yidongbiaoqian"></i>
    </div>
</template>
<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { useHubbleStore } from '@/stores/hubble'
import ChatList from './chatList/index.vue'

// Props
const props = defineProps({
    topicId: {
        type: String,
        default: '',
    },
})

// Store
const hubbleStore = useHubbleStore()
const route = useRoute()
// Reactive data
const chatInitLoading = ref(true)
const messages = ref([])
const continuousMessages = ref([])
const showContinuousChat = ref(false)
const startChatId = ref('')
const startContinuousChatId = ref('')
const multiTurnChatBeginId = ref('')
const loadChatSize = ref(20)
const noMore = ref(false)
const noMoreContinuous = ref(false)
const isLoadingMore = ref(false)
const input = ref('')
const typing = ref(false)
const showDialog = ref(false)
const prepareTyping = ref(false)
const typingInterval = ref(null)
const hasInit = ref(false)
const initTimeout = ref(null)
const isEn = ref(false)
const showSuggestion = ref(false)
const loadSuggestion = ref(false)
const suggestions = ref([])
const currentSuggestion = ref({})
const promptPlugins = ref([])
const inputActive = ref(false)
const currentPrompt = ref({})
const currentPromptIndex = ref(0)
const languageSwitchVisible = ref(false)
// Computed properties
const btnDisabled = computed(() => {
    return typing.value || prepareTyping.value
})

const currentSuggestions = computed(() => {
    return suggestions.value.filter(el => el.questionScope === (hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'))
})

const filterPromptList = computed(() => {
    if (input.value[0] !== '/') {
        return []
    }
    const inputValue = input.value.slice(1)
    const list = promptPlugins.value.filter(el => el.scope === (hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'))
    return inputValue ? list.filter(el => containsAllChars(inputValue, el.shortName) || containsAllChars(inputValue, el.displayName)) : list
})

const showPrompt = computed(() => {
    return !currentPrompt.value.shortName && inputActive.value && filterPromptList.value.length > 0
})

const currentMessages = computed(() => {
    return showContinuousChat.value ? continuousMessages.value : messages.value
})
// Watchers
watch(() => hubbleStore.preQuote.content, (val) => {
    if (val) {
        currentPrompt.value = {}
    }
})

watch(() => currentPrompt.value.shortName, (val) => {
    nextTick(() => {
        const inputElement = document.querySelector('.hubble-chat__input')?.querySelector('.el-input__inner')
        if (inputElement) {
            if (val) {
                const inputPlugin = document.querySelector('.hubble-chat__input-plugin')
                const width = inputPlugin?.offsetWidth || 0
                inputElement.style.paddingLeft = `${width}px`
            } else {
                inputElement.style.paddingLeft = '10px'
            }
        }
    })
})

watch(messages, () => {
    nextTick(() => {
        scrollList()
    })
}, { immediate: true, deep: true })

watch(prepareTyping, (val) => {
    const message = currentMessages.value[0] || {}
    if (val) {
        typingInterval.value = setInterval(() => {
            if (message.answer.length === 3) {
                message.answer = '.'
            } else {
                message.answer += '.'
            }
        }, 500)
    } else {
        clearInterval(typingInterval.value)
    }
}, { immediate: true })

watch(filterPromptList, () => {
    currentPromptIndex.value = 0
})
// Methods
const switchEnLanguage = (bool) => {
    isEn.value = bool
    languageSwitchVisible.value = false
}

const handleDownloadChat = (withChat = true) => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    // 原接口: const downloadUrl = `/web/hubble/topic/${props.topicId}/documents/${hubbleStore.documentId}/download${withChat ? '-with-quote-chats' : ''}`
    // download(downloadUrl)
    console.log('Download chat called with withChat:', withChat)
}

const handleShowContinuousChat = (chatId) => {
    continuousMessages.value = []
    noMoreContinuous.value = false
    multiTurnChatBeginId.value = chatId
    startContinuousChatId.value = ''
    showContinuousChat.value = true
    getContinuousChat()
}
const getContinuousChat = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    isLoadingMore.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        // 原接口调用成功后的处理逻辑
        const historyChats = [] // 模拟空数据
        const topicLength = historyChats.length
        startContinuousChatId.value = topicLength ? historyChats[historyChats.length - 1].chatId : ''
        noMoreContinuous.value = topicLength < loadChatSize.value
        continuousMessages.value = continuousMessages.value.concat(historyChats.map(el => {
            return {
                ...el,
                chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                quote: el.questionDocumentQuote?.content || '',
            }
        }))
        isLoadingMore.value = false
    }, 100)
}

const handleLoadMoreContinuousChat = (event) => {
    if (noMoreContinuous.value || isLoadingMore.value) {
        return
    }
    const list = event.target
    if (list.scrollTop + list.scrollHeight === list.clientHeight) {
        getHistoryChats()
    }
}
const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
        if (showPrompt.value) {
            event.preventDefault()
            return handleSelectPlugin(filterPromptList.value[currentPromptIndex.value])
        }
        !event.shiftKey && handleSendMessage()
    } else if (event.keyCode === 8 && !input.value && currentPrompt.value.displayName) {
        currentPrompt.value = {}
        setTimeout(() => {
            input.value = '/'
        }, 50)
    } else if (event.key === 'ArrowDown') {
        if (showPrompt.value) {
            currentPromptIndex.value = (currentPromptIndex.value + 1) % filterPromptList.value.length
        }
    } else if (event.key === 'ArrowUp') {
        if (showPrompt.value) {
            currentPromptIndex.value = (currentPromptIndex.value - 1 + filterPromptList.value.length) % filterPromptList.value.length
        }
    }
}

const handleSelectPlugin = (plugin) => {
    currentPrompt.value = plugin
    input.value = ''
}

const inputBlur = () => {
    setTimeout(() => {
        inputActive.value = false
    }, 100)
}

const containsAllChars = (str, target) => {
    const regex = new RegExp([...str.toLowerCase()].map(c => `${c}.*`).join(''))
    return regex.test(target.toLowerCase())
}

const scrollList = () => {
    const element = document.querySelector('.hubble-chat__body')
    if (element) {
        element.scrollTop = element.scrollHeight
    }
}

const clearQuote = () => {
    hubbleStore.setQuote({})
}
const handleSendMessage = async () => {
    if (input.value.trim()) {
        const messagesRef = currentMessages.value
        messagesRef.unshift({
            question: input.value.trim(),
            questionDocumentQuote: hubbleStore.preQuote,
            chatTime: new Date().toLocaleString(),
            quote: hubbleStore.preQuote.content,
            answer: '',
        })
        sendMessage()
        input.value = ''
    }
}

const handleScopeAndPlugin = () => {
    if (currentPrompt.value.pluginName) {
        return {
            questionScope: currentPrompt.value.scope,
            enablePlugin: currentPrompt.value.pluginName,
        }
    } else if (currentSuggestion.value.pluginName) {
        return {
            questionScope: currentSuggestion.value.questionScope,
            enablePlugin: currentSuggestion.value.pluginName,
        }
    }
    return {
        questionScope: hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT',
        enablePlugin: hubbleStore.preQuote.content ? 'FREE_CHAT_QUOTE' : 'FREE_CHAT_DOCUMENT',
    }
}
const sendMessage = () => {
    // const scopeAndPlugin = handleScopeAndPlugin() // 接口调用逻辑已移除
    currentPrompt.value = {}
    currentSuggestion.value = {}
    prepareTyping.value = true

    setTimeout(() => {
        clearQuote()
    }, 50)

    // 接口调用逻辑已移除，根据需求只保留回调部分
    // 模拟接口调用成功后的回调
    setTimeout(() => {
        nextTick(() => {
            const answer = '这是模拟的AI回答内容。' // 模拟回答
            const messagesRef = currentMessages.value
            const message = messagesRef[0]

            nextTick(() => {
                message.sendContent = answer
                animateMessage().then(() => {
                    // 模拟接口返回的数据结构
                    const mockResponse = {
                        answer: answer,
                        chatTime: new Date().toISOString(),
                        chatId: 'mock-chat-id-' + Date.now(),
                        questionDocumentQuote: hubbleStore.preQuote
                    }

                    Object.assign(messagesRef[0], {
                        ...messagesRef[0],
                        ...mockResponse,
                        chatTime: dayjs(mockResponse.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                        quote: mockResponse.questionDocumentQuote?.content || '',
                    })
                })
            })
        })
        prepareTyping.value = false
    }, 1000)
}
const animateMessage = () => {
    typing.value = true
    const message = currentMessages.value[0]
    const content = message.sendContent
    let i = 0

    return new Promise(resolve => {
        const interval = setInterval(() => {
            message.answer = content.slice(0, i + 1)
            scrollList()
            i++
            if (i === content.length) {
                clearInterval(interval)
                typing.value = false
                resolve()
            }
        }, 50)
    })
}

const initSuggestions = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    loadSuggestion.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        suggestions.value = [] // 模拟空建议列表
        loadSuggestion.value = false
    }, 500)
}

const handleSendSuggestion = (suggestion) => {
    input.value = suggestion.suggestQuestionContent
    currentSuggestion.value = suggestion
    showSuggestion.value = false
    handleSendMessage()
}

const handleSelectQuote = (quote) => {
    const { quoteCoordinate, pageNumber } = quote
    hubbleStore.setQuotePage(pageNumber)
    hubbleStore.setQuoteCoordinate(quoteCoordinate)
    hubbleStore.setStartSelectQuote(true)
}
const initMove = () => {
    // 移动功能需要在组件挂载后初始化，使用 refs
    // 这部分逻辑会在 onMounted 中处理
}

const initPrompt = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    // const iconMap = {
    //     contractDetail: 'el-icon-ssq-Hubblehetongxiangqing',
    //     contractContent: 'el-icon-ssq-Hubblehetongneirong',
    // }

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        promptPlugins.value = [] // 模拟空插件列表
    }, 100)
}
const waitChatInit = () => {
    return new Promise((resolve, reject) => {
        // 接口调用逻辑已移除，根据需求只保留回调部分
        // 模拟初始化完成
        setTimeout(() => {
            chatInitLoading.value = false
            resolve()
        }, 1000)
    })
}

const handleScroll = async (event) => {
    if (noMore.value || isLoadingMore.value) {
        return
    }
    const list = event.target
    if (list.scrollTop + list.scrollHeight === list.clientHeight) {
        getHistoryChats()
    }
}

const getHistoryChats = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    isLoadingMore.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        const historyChats = [] // 模拟空历史记录
        const topicLength = historyChats.length
        startChatId.value = topicLength ? historyChats[historyChats.length - 1].chatId : ''
        noMore.value = topicLength < loadChatSize.value
        messages.value = messages.value.concat(historyChats.map(el => {
            return {
                ...el,
                chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                quote: el.questionDocumentQuote?.content || '',
            }
        }))
        isLoadingMore.value = false
    }, 500)
}

const initMessages = () => {
    startChatId.value = ''
    messages.value = []
    getHistoryChats()
}
const init = async () => {
    // Socket.io 相关代码已移除
    await waitChatInit()
    initMove()
    initPrompt()
    initSuggestions()
    initMessages()
}

const handleShowSuggestion = () => {
    showSuggestion.value = !showSuggestion.value
    // Socket.io 相关代码已移除
}

// 生命周期钩子
onMounted(() => {
    init()

    // 初始化拖拽功能
    nextTick(() => {
        const chatHandle = document.querySelector('.hubble-chat__move')
        const chat = document.querySelector('.hubble-chat')

        if (chatHandle && chat) {
            let isDragging = false
            let lastX

            chatHandle.addEventListener('mousedown', (e) => {
                isDragging = true
                lastX = e.clientX
                document.body.style.userSelect = 'none'
            })

            document.addEventListener('mousemove', (e) => {
                const chatWidth = chat.offsetWidth
                const delta = lastX - e.clientX
                if (!isDragging || (chatWidth <= 450 && delta < 0)) {
                    return
                }
                chat.style.width = `${chatWidth + delta}px`
                lastX = e.clientX
            })

            document.addEventListener('mouseup', () => {
                isDragging = false
                document.body.style.userSelect = 'unset'
            })
        }
    })
})

onBeforeUnmount(() => {
    clearQuote()
    if (initTimeout.value) {
        clearTimeout(initTimeout.value)
    }
})
</script>

<style lang="scss">
@import './index.scss'
</style>
