<template>
    <div class="hubble-page__content">
        <Document
            class="hubble-page__content-document"
            :topicId="topicId"
        ></Document>
        <ChatView :key="topicId" class="hubble-page__content-chat" :topicId="topicId"></ChatView>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import ChatView from './chatView/index.vue'
import Document from './document/index.vue'

const route = useRoute()
// 使用默认的 topicId 或从路由参数获取
const topicId = ref(route.params.topicId || 'default-topic')

onMounted(() => {
    // 添加全屏样式类
    document.body.classList.add('hubble-fullscreen')
    document.getElementById('app').classList.add('hubble-fullscreen')

    // 接口调用逻辑已移除，根据需求只保留回调部分
    // 原接口: this.$http.post(`/web/hubble/topic/${this.topicId}/init-document-free-chat-plugin`)
    console.log('Agent component mounted, topicId:', topicId.value)
})

onBeforeUnmount(() => {
    // 移除全屏样式类
    document.body.classList.remove('hubble-fullscreen')
    document.getElementById('app').classList.remove('hubble-fullscreen')
})
</script>
