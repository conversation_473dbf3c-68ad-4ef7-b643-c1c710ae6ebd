import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useHubbleStore = defineStore('hubble', () => {
  // State
  const preQuote = ref({})
  const documentInfo = ref({})
  const quotePage = ref(null)
  const quoteCoordinate = ref({})
  const startSelectQuote = ref(false)
  const showSlider = ref(false)

  // Getters
  const documentId = computed(() => documentInfo.value.documentId)

  // Actions
  const setQuote = (quote) => {
    preQuote.value = quote
  }

  const setDocumentInfo = (info) => {
    documentInfo.value = info
  }

  const toggleSlider = () => {
    showSlider.value = !showSlider.value
  }

  const setQuotePage = (page) => {
    quotePage.value = page
  }

  const setQuoteCoordinate = (coordinate) => {
    quoteCoordinate.value = coordinate
  }

  const setStartSelectQuote = (value) => {
    startSelectQuote.value = value
  }

  return {
    // State
    preQuote,
    documentInfo,
    quotePage,
    quoteCoordinate,
    startSelectQuote,
    showSlider,
    
    // Getters
    documentId,
    
    // Actions
    setQuote,
    setDocumentInfo,
    toggleSlider,
    setQuotePage,
    setQuoteCoordinate,
    setStartSelectQuote
  }
})
