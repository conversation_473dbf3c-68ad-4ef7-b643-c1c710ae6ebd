<template>
    <div ref="chat" class="hubble-chat">
        <div class="hubble-chat__header">
            <span>
                <img src="~img/AIBot.png" class="avatar" alt="">
                Hubble
            </span>
            <div class="operate">
                <el-popover
                    trigger="click"
                    popper-class="download-popover"
                    v-model="languageSwitchVisible"
                >
                    <el-tooltip slot="reference" :open-delay="500" effect="dark" content="输出语言切换" placement="top">
                        <i class="el-icon-ssq-diqiu" id="guide-lang"></i>
                    </el-tooltip>
                    <ul>
                        <li :class="isEn ? '' : 'active'" @click="switchEnLanguage(false)">中文语言</li>
                        <li :class="isEn ? 'active' : ''" @click="switchEnLanguage(true)">英文语言</li>
                    </ul>
                </el-popover>
                <el-popover
                    trigger="click"
                    popper-class="download-popover"
                >
                    <el-tooltip slot="reference" :open-delay="500" effect="dark" content="文档下载" placement="top">
                        <i class="el-icon-ssq--bs-xiazai"></i>
                    </el-tooltip>
                    <ul>
                        <li @click="handleDownloadChat(false)">源文件</li>
                        <li @click="handleDownloadChat">源文件 (携带对话)</li>
                    </ul>
                </el-popover>
            </div>
        </div>
        <div class="hubble-chat__body" @scroll="handleScroll">
            <div class="hubble-chat__loading" v-if="chatInitLoading">
                <img src="~img/loading2.gif" alt=""><br>
                <span>文档解析中</span>
            </div>
            <template v-else>
                <ChatList
                    :messages="messages"
                    :typing="typing"
                    @selectQuote="handleSelectQuote"
                    @showContinuousChat="handleShowContinuousChat"
                    @loadMore="handleScroll"
                ></ChatList>
                <transition name="fade">
                    <div v-show="showContinuousChat" class="continuous-chat">
                        <ChatList
                            isContinuousChat
                            :messages="continuousMessages"
                            :typing="typing"
                            @selectQuote="handleSelectQuote"
                            @loadMore="handleLoadMoreContinuousChat"
                        ></ChatList>
                        <div class="continuous-exit">
                            <span>连续对话模式：</span>
                            <span class="exit" @click="showContinuousChat = false">退出</span>
                        </div>
                    </div>
                </transition>
            </template>
        </div>
        <div class="hubble-chat__dialog">
            <transition name="slide">
                <div class="hubble-chat__quote" v-show="!!preQuote.content">
                    <div class="hubble-chat__quote-header">
                        <span>引用内容：</span>
                        <i class="el-icon-ssq-guanbi" @click="clearQuote"></i>
                    </div>
                    <div class="hubble-chat__quote-body chat-quote" @click="handleSelectQuote(preQuote)">{{ preQuote.content }}</div>
                </div>
            </transition>
            <transition name="slide">
                <div :class="`hubble-chat__suggestion ${!preQuote.content ? 'without-quote' : ''}`" v-show="showSuggestion">
                    <div class="hubble-chat__suggestion-header">
                        <i class="el-icon-ssq-gaoliangtishiH5"></i>
                        建议问题：
                        <i class="el-icon-ssq-guanbi" @click="showSuggestion = false"></i>
                    </div>
                    <ul class="hubble-chat__suggestion-list">
                        <li
                            class="hubble-chat__suggestion-item"
                            v-for="(suggestion, i) in currentSuggestions"
                            :key="i"
                            @click="handleSendSuggestion(suggestion)"
                        >
                            <span>{{ suggestion.suggestQuestionContent }}</span>
                            <i class="el-icon-ssq-fajianxiang"></i>
                        </li>
                        <li class="hubble-chat__suggestion-item empty" v-if="!currentSuggestions.length">
                            <img src="~img/loading.gif" v-if="loadSuggestion" alt="">
                            <span v-else>没有数据</span>
                        </li>
                    </ul>
                </div>
            </transition>
        </div>
        <div class="hubble-chat__footer" v-if="!chatInitLoading">
            <div :class="`hubble-chat__input ${currentPrompt.displayName ? 'line-feed' : ''}`" @keydown="handleKeyDown">
                <ul class="hubble-chat__prompt" v-show="showPrompt">
                    <li
                        v-for="(plugin, index) in filterPromptList"
                        :class="`hubble-chat__prompt-plugin ${ index === currentPromptIndex ? 'active' : '' }`"
                        @click="handleSelectPlugin(plugin)"
                        :key="plugin.pluginName"
                    >
                        <i :class="plugin.icon" v-if="plugin.icon"></i>
                        <div>
                            <p>
                                {{ plugin.displayName }}
                                <span>（{{ plugin.shortName }}）</span>
                            </p>
                            <span>{{ plugin.desc }}</span>
                        </div>
                    </li>
                </ul>
                <span ref="inputPlugin" class="hubble-chat__input-plugin" v-show="!!currentPrompt.displayName">/ {{ currentPrompt.displayName }}</span>
                <el-input
                    id="guide-text"
                    ref="inputItem"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 6}"
                    resize="none"
                    placeholder="请输入您的问题"
                    v-model="input"
                    :disabled="btnDisabled"
                    @blur="inputBlur"
                    @focus="inputActive = true"
                ></el-input>
            </div>
            <div class="hubble-chat__footer-operate">
                <el-tooltip :open-delay="500" effect="dark" content="建议问题" placement="top">
                    <i id="guide-suggestion" class="operate-icon el-icon-ssq-Hubbletishi" @click="handleShowSuggestion"></i>
                </el-tooltip>
                <el-button type="primary" :disabled="btnDisabled" @click="handleSendMessage">发送</el-button>
            </div>
        </div>
        <i ref="move" class="hubble-chat__move el-icon-ssq-yidongbiaoqian"></i>
    </div>
</template>
<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { useHubbleStore } from '@/stores/hubble'
import ChatList from './chatList/index.vue'

// Props
const props = defineProps({
    topicId: {
        type: String,
        default: '',
    },
})

// Store
const hubbleStore = useHubbleStore()
const route = useRoute()
// Reactive data
const chatInitLoading = ref(true)
const messages = ref([])
const continuousMessages = ref([])
const showContinuousChat = ref(false)
const startChatId = ref('')
const startContinuousChatId = ref('')
const multiTurnChatBeginId = ref('')
const loadChatSize = ref(20)
const noMore = ref(false)
const noMoreContinuous = ref(false)
const isLoadingMore = ref(false)
const input = ref('')
const typing = ref(false)
const showDialog = ref(false)
const prepareTyping = ref(false)
const typingInterval = ref(null)
const hasInit = ref(false)
const initTimeout = ref(null)
const isEn = ref(false)
const showSuggestion = ref(false)
const loadSuggestion = ref(false)
const suggestions = ref([])
const currentSuggestion = ref({})
const promptPlugins = ref([])
const inputActive = ref(false)
const currentPrompt = ref({})
const currentPromptIndex = ref(0)
const languageSwitchVisible = ref(false)
// Computed properties
const btnDisabled = computed(() => {
    return typing.value || prepareTyping.value
})

const currentSuggestions = computed(() => {
    return suggestions.value.filter(el => el.questionScope === (hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'))
})

const filterPromptList = computed(() => {
    if (input.value[0] !== '/') {
        return []
    }
    const inputValue = input.value.slice(1)
    const list = promptPlugins.value.filter(el => el.scope === (hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'))
    return inputValue ? list.filter(el => containsAllChars(inputValue, el.shortName) || containsAllChars(inputValue, el.displayName)) : list
})

const showPrompt = computed(() => {
    return !currentPrompt.value.shortName && inputActive.value && filterPromptList.value.length > 0
})

const currentMessages = computed(() => {
    return showContinuousChat.value ? continuousMessages.value : messages.value
})
// Watchers
watch(() => hubbleStore.preQuote.content, (val) => {
    if (val) {
        currentPrompt.value = {}
    }
})

watch(() => currentPrompt.value.shortName, (val) => {
    nextTick(() => {
        const inputElement = document.querySelector('.hubble-chat__input')?.querySelector('.el-input__inner')
        if (inputElement) {
            if (val) {
                const inputPlugin = document.querySelector('.hubble-chat__input-plugin')
                const width = inputPlugin?.offsetWidth || 0
                inputElement.style.paddingLeft = `${width}px`
            } else {
                inputElement.style.paddingLeft = '10px'
            }
        }
    })
})

watch(messages, () => {
    nextTick(() => {
        scrollList()
    })
}, { immediate: true, deep: true })

watch(prepareTyping, (val) => {
    const message = currentMessages.value[0] || {}
    if (val) {
        typingInterval.value = setInterval(() => {
            if (message.answer.length === 3) {
                message.answer = '.'
            } else {
                message.answer += '.'
            }
        }, 500)
    } else {
        clearInterval(typingInterval.value)
    }
}, { immediate: true })

watch(filterPromptList, () => {
    currentPromptIndex.value = 0
})
// Methods
const switchEnLanguage = (bool) => {
    isEn.value = bool
    languageSwitchVisible.value = false
}

const handleDownloadChat = (withChat = true) => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    // 原接口: const downloadUrl = `/web/hubble/topic/${props.topicId}/documents/${hubbleStore.documentId}/download${withChat ? '-with-quote-chats' : ''}`
    // download(downloadUrl)
    console.log('Download chat called with withChat:', withChat)
}

const handleShowContinuousChat = (chatId) => {
    continuousMessages.value = []
    noMoreContinuous.value = false
    multiTurnChatBeginId.value = chatId
    startContinuousChatId.value = ''
    showContinuousChat.value = true
    getContinuousChat()
}
const getContinuousChat = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    isLoadingMore.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        // 原接口调用成功后的处理逻辑
        const historyChats = [] // 模拟空数据
        const topicLength = historyChats.length
        startContinuousChatId.value = topicLength ? historyChats[historyChats.length - 1].chatId : ''
        noMoreContinuous.value = topicLength < loadChatSize.value
        continuousMessages.value = continuousMessages.value.concat(historyChats.map(el => {
            return {
                ...el,
                chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                quote: el.questionDocumentQuote?.content || '',
            }
        }))
        isLoadingMore.value = false
    }, 100)
}

const handleLoadMoreContinuousChat = (event) => {
    if (noMoreContinuous.value || isLoadingMore.value) {
        return
    }
    const list = event.target
    if (list.scrollTop + list.scrollHeight === list.clientHeight) {
        getHistoryChats()
    }
}
const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
        if (showPrompt.value) {
            event.preventDefault()
            return handleSelectPlugin(filterPromptList.value[currentPromptIndex.value])
        }
        !event.shiftKey && handleSendMessage()
    } else if (event.keyCode === 8 && !input.value && currentPrompt.value.displayName) {
        currentPrompt.value = {}
        setTimeout(() => {
            input.value = '/'
        }, 50)
    } else if (event.key === 'ArrowDown') {
        if (showPrompt.value) {
            currentPromptIndex.value = (currentPromptIndex.value + 1) % filterPromptList.value.length
        }
    } else if (event.key === 'ArrowUp') {
        if (showPrompt.value) {
            currentPromptIndex.value = (currentPromptIndex.value - 1 + filterPromptList.value.length) % filterPromptList.value.length
        }
    }
}

const handleSelectPlugin = (plugin) => {
    currentPrompt.value = plugin
    input.value = ''
}

const inputBlur = () => {
    setTimeout(() => {
        inputActive.value = false
    }, 100)
}

const containsAllChars = (str, target) => {
    const regex = new RegExp([...str.toLowerCase()].map(c => `${c}.*`).join(''))
    return regex.test(target.toLowerCase())
}

const scrollList = () => {
    const element = document.querySelector('.hubble-chat__body')
    if (element) {
        element.scrollTop = element.scrollHeight
    }
}

const clearQuote = () => {
    hubbleStore.setQuote({})
}
const handleSendMessage = async () => {
    if (input.value.trim()) {
        const messagesRef = currentMessages.value
        messagesRef.unshift({
            question: input.value.trim(),
            questionDocumentQuote: hubbleStore.preQuote,
            chatTime: new Date().toLocaleString(),
            quote: hubbleStore.preQuote.content,
            answer: '',
        })
        sendMessage()
        input.value = ''
    }
}

const handleScopeAndPlugin = () => {
    if (currentPrompt.value.pluginName) {
        return {
            questionScope: currentPrompt.value.scope,
            enablePlugin: currentPrompt.value.pluginName,
        }
    } else if (currentSuggestion.value.pluginName) {
        return {
            questionScope: currentSuggestion.value.questionScope,
            enablePlugin: currentSuggestion.value.pluginName,
        }
    }
    return {
        questionScope: hubbleStore.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT',
        enablePlugin: hubbleStore.preQuote.content ? 'FREE_CHAT_QUOTE' : 'FREE_CHAT_DOCUMENT',
    }
}
const sendMessage = () => {
    // const scopeAndPlugin = handleScopeAndPlugin() // 接口调用逻辑已移除
    currentPrompt.value = {}
    currentSuggestion.value = {}
    prepareTyping.value = true

    setTimeout(() => {
        clearQuote()
    }, 50)

    // 接口调用逻辑已移除，根据需求只保留回调部分
    // 模拟接口调用成功后的回调
    setTimeout(() => {
        nextTick(() => {
            const answer = '这是模拟的AI回答内容。' // 模拟回答
            const messagesRef = currentMessages.value
            const message = messagesRef[0]

            nextTick(() => {
                message.sendContent = answer
                animateMessage().then(() => {
                    // 模拟接口返回的数据结构
                    const mockResponse = {
                        answer: answer,
                        chatTime: new Date().toISOString(),
                        chatId: 'mock-chat-id-' + Date.now(),
                        questionDocumentQuote: hubbleStore.preQuote
                    }

                    Object.assign(messagesRef[0], {
                        ...messagesRef[0],
                        ...mockResponse,
                        chatTime: dayjs(mockResponse.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                        quote: mockResponse.questionDocumentQuote?.content || '',
                    })
                })
            })
        })
        prepareTyping.value = false
    }, 1000)
}
const animateMessage = () => {
    typing.value = true
    const message = currentMessages.value[0]
    const content = message.sendContent
    let i = 0

    return new Promise(resolve => {
        const interval = setInterval(() => {
            message.answer = content.slice(0, i + 1)
            scrollList()
            i++
            if (i === content.length) {
                clearInterval(interval)
                typing.value = false
                resolve()
            }
        }, 50)
    })
}

const initSuggestions = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    loadSuggestion.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        suggestions.value = [] // 模拟空建议列表
        loadSuggestion.value = false
    }, 500)
}

const handleSendSuggestion = (suggestion) => {
    input.value = suggestion.suggestQuestionContent
    currentSuggestion.value = suggestion
    showSuggestion.value = false
    handleSendMessage()
}

const handleSelectQuote = (quote) => {
    const { quoteCoordinate, pageNumber } = quote
    hubbleStore.setQuotePage(pageNumber)
    hubbleStore.setQuoteCoordinate(quoteCoordinate)
    hubbleStore.setStartSelectQuote(true)
}
const initMove = () => {
    // 移动功能需要在组件挂载后初始化，使用 refs
    // 这部分逻辑会在 onMounted 中处理
}

const initPrompt = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    // const iconMap = {
    //     contractDetail: 'el-icon-ssq-Hubblehetongxiangqing',
    //     contractContent: 'el-icon-ssq-Hubblehetongneirong',
    // }

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        promptPlugins.value = [] // 模拟空插件列表
    }, 100)
}
const waitChatInit = () => {
    return new Promise((resolve, reject) => {
        // 接口调用逻辑已移除，根据需求只保留回调部分
        // 模拟初始化完成
        setTimeout(() => {
            chatInitLoading.value = false
            resolve()
        }, 1000)
    })
}

const handleScroll = async (event) => {
    if (noMore.value || isLoadingMore.value) {
        return
    }
    const list = event.target
    if (list.scrollTop + list.scrollHeight === list.clientHeight) {
        getHistoryChats()
    }
}

const getHistoryChats = () => {
    // 接口调用逻辑已移除，根据需求只保留回调部分
    isLoadingMore.value = true

    // 模拟接口调用成功后的回调
    setTimeout(() => {
        const historyChats = [] // 模拟空历史记录
        const topicLength = historyChats.length
        startChatId.value = topicLength ? historyChats[historyChats.length - 1].chatId : ''
        noMore.value = topicLength < loadChatSize.value
        messages.value = messages.value.concat(historyChats.map(el => {
            return {
                ...el,
                chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                quote: el.questionDocumentQuote?.content || '',
            }
        }))
        isLoadingMore.value = false
    }, 500)
}

const initMessages = () => {
    startChatId.value = ''
    messages.value = []
    getHistoryChats()
}
const init = async () => {
    // Socket.io 相关代码已移除
    await waitChatInit()
    initMove()
    initPrompt()
    initSuggestions()
    initMessages()
}

const handleShowSuggestion = () => {
    showSuggestion.value = !showSuggestion.value
    // Socket.io 相关代码已移除
}

// 生命周期钩子
onMounted(() => {
    init()

    // 初始化拖拽功能
    nextTick(() => {
        const chatHandle = document.querySelector('.hubble-chat__move')
        const chat = document.querySelector('.hubble-chat')

        if (chatHandle && chat) {
            let isDragging = false
            let lastX

            chatHandle.addEventListener('mousedown', (e) => {
                isDragging = true
                lastX = e.clientX
                document.body.style.userSelect = 'none'
            })

            document.addEventListener('mousemove', (e) => {
                const chatWidth = chat.offsetWidth
                const delta = lastX - e.clientX
                if (!isDragging || (chatWidth <= 450 && delta < 0)) {
                    return
                }
                chat.style.width = `${chatWidth + delta}px`
                lastX = e.clientX
            })

            document.addEventListener('mouseup', () => {
                isDragging = false
                document.body.style.userSelect = 'unset'
            })
        }
    })
})

onBeforeUnmount(() => {
    clearQuote()
    if (initTimeout.value) {
        clearTimeout(initTimeout.value)
    }
})
</script>

<style lang="scss">
$theme-color: #127fd2;
.hubble-chat{
    *{
        box-sizing: border-box;
    }
    height: calc(100vh - 60px - 35px);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 9;
    background: #fff;
    &__loading{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #ccc;
    }
    &__header{
        width: 100%;
        font-size: 14px;
        height: 55px;
        padding: 0 20px;
        line-height: 55px;
        background: #fff;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        img{
            width: 20px;
            height: 20px;
            position: relative;
            top: 5px;
        }
        .operate{
            i{
                cursor: pointer;
                padding: 8px;
                display: inline-block;
                &:hover{
                    background: #f6f6f6;
                }
                &+i{
                    margin-left: 10px;
                }
            }
        }
    }
    &__body{
        overflow: auto;
        padding-top: 15px;
        font-size: 12px;
        display: flex;
        flex-direction: column-reverse;
        flex-grow: 1;
        .message{
            margin: 0 20px;
            padding: 10px 0 20px;
            &+.message{
                border-top: 1px solid #eee;
            }
        }
        .question, .answer{
            display: flex;
            flex-direction:row;
            margin: 20px 0 10px;
            .message-content{
                min-width: 15px;
                max-width: calc(100% - 45px);
                padding: 10px;
                white-space: pre-line;
                word-break: break-all;
                line-height: 22px;
                border-radius: 4px;
                background: #D6E9FF;
            }
            .avatar{
                width: 30px;
                height: 30px;
                margin-right: 15px;
            }
            &.question{
                .message-content{
                    background: #F0F1F3;
                }
            }
            .cursor{
                display: inline-block;
                width: 1px;
                height: 16px;
                position: relative;
                top: 2px;
                background: #fff;
                animation: blink 1s infinite;
            }
            .explain{
                color: #bbb;
                font-size: 12px;
            }
        }
        .time, .related{
            color: #999;
            margin-left: 45px;
            overflow: hidden;
        }
        .related{
            span {
                color: $theme-color;
                cursor: pointer;
                padding: 0 5px;
                &+span{
                    border-left: 1px solid #ccc;
                }
            }
            .operate-icon{
                font-size: 16px;
                cursor: pointer;
                float: right;
                margin-left: 5px;
            }
        }
        &-empty{
            width: 100%;
            height: 200px;
            position: absolute;
            top: 50%;
            margin-top: -100px;
            text-align: center;
            color: #999;
            font-size: 12px;
            img{
                width: 160px;
                display: block;
                margin: 0 auto 12px;
            }
        }
        .continuous-chat{
            position: absolute;
            width: 100%;
            height: calc(100vh - 115px - 72px);
            top: 55px;
            right: 0;
            overflow: hidden;
            background: #fff;
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            .hubble-chat__body-box{
                max-height: calc(100% - 38px);
                overflow: auto;
            }
            .continuous-exit{
                background: #0C8AEE 100%;
                color: #fff;
                line-height: 38px;
                padding: 0 20px;
                span.exit{
                    float: right;
                    cursor: pointer;
                }
            }
        }
    }
    &__footer{
        background: #F8F8F8;
        padding: 20px;
        position: relative;
        z-index: 9;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        &-operate{
            display: flex;
            position: absolute;
            right: 20px;
            bottom: 20px;
            i.operate-icon{
                font-size: 20px;
                flex: 1;
                border: 1px solid rgba(221,221,221,1);
                border-radius: 2px;
                color: $theme-color;
                display: inline-block;
                width: 34px;
                height: 34px;
                margin-left: 5px;
                text-align: center;
                line-height: 32px;
                background: #fff;
                cursor: pointer;
                &:hover{
                    background: #f8f8f8;
                }
            }
            .el-button{
                margin-left: 5px;
                padding: 0;
                height: 34px;
                line-height: 34px;
                width: 50px;
                text-align: center;
                background: $theme-color;
                border-color: $theme-color;
                &:hover{
                    background: #20a0ff;
                    border-color: #20a0ff;
                }
            }
        }
    }
    &__input{
        width: calc(100% - 95px);
        position: relative;
        background: #fff;
        &-plugin{
            position: absolute;
            font-size: 12px;
            line-height: 30px;
            color: #0C8AEE;
            padding: 0 5px;
            z-index: 2;
            padding-left: 10px;
        }
        &.line-feed{
            .el-textarea__inner{
                text-indent: 60px;
            }
        }
        .el-textarea__inner{
            min-height: 32px !important;
        }
    }
    &__prompt{
        width: 100%;
        background: #fff;
        position: absolute;
        z-index: 9;
        bottom: 60px;
        box-shadow: 0px 4px 26px 0px rgba(0,0,0,0.08);
        border-radius: 2px;
        padding: 10px 0;
        &-plugin{
            height: 60px;
            padding: 10px 0 10px 18px;
            font-size: 14px;
            display: flex;
            cursor: pointer;
            i{
                font-size: 16px;
                height: 24px;
                padding: 3px;
                background: #FFFFFF;
                border: 1px solid rgba(238,238,238,1);
                box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.06);
                border-radius: 4px;
                margin: 6px 15px 0 0;
            }
            &:hover, &.active{
                color: unset;
                background: #f8f8f8;
            }
            span{
                font-size: 12px;
                color: #999;
            }
        }
    }
    &__dialog{
        width: 100%;
        font-size: 12px;
        position: absolute;
        bottom: 72px;
        border-bottom: 1px solid #eee;
    }
    &__quote{
        font-size: 12px;
        padding: 20px;
        background: #F8F8F8;
        box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        &-header{
            position: relative;
            i{
                color: #ccc;
                position: absolute;
                cursor: pointer;
                right: 0;
                top: 0;
            }
        }
        &-body{
            margin-top: 5px;
        }
    }
    &__suggestion{
        background: #fff;
        font-size: 12px;
        position: relative;
        z-index: 9;
        &.without-quote{
            box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        }
        &-header{
            height: 50px;
            line-height: 50px;
            font-size: 14px;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 20px;
            i.el-icon-ssq-guanbi{
                font-size: 12px;
                color: #ccc;
                position: absolute;
                top: 20px;
                right: 20px;
                cursor: pointer;
            }
        }
        &-list{
            padding: 14px 20px 20px 30px;
            line-height: 22px;
            li{
                list-style: auto;
                cursor: pointer;
                &:hover span{
                    background: #f8f8f8;
                }
                i{
                    color: $theme-color;
                }
                &+li{
                    margin-top: 10px;
                }
                &.empty{
                    list-style-type: none;
                    text-align: center;
                }
            }
        }
    }
    &__move{
        position: absolute;
        top: 50%;
        margin-top: -30px;
        left: -18px;
        cursor: col-resize;
        z-index: 999;
        background: #fff;
        box-shadow: -6px 0px 8px 0px rgba(0, 0, 0, 0.1);
        padding: 22px 1px;
        border-radius: 5px 0 0 5px;
    }

    .chat-quote{
        line-height: 22px;
        position: relative;
        padding-left: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        &:hover{
            color: #666;
            background: #E2E4E8;
        }
        &::before{
            content: '"';
            position: absolute;
            top: 8px;
            left: 0px;
            color: #999;
            font-size: 40px;
        }
        &::after{
            content: '';
            position: absolute;
            top: 20px;
            left: 2px;
            width: 3px;
            height: calc(100% - 25px);
            background: #999;
        }
    }
    .slide-enter-active, .slide-leave-active, .fade-enter-active, .fade-leave-active {
        transition: all 0.2s ease;
    }
    .slide-enter, .slide-leave-to {
        transform: translateY(100%);
    }
    .fade-enter, .fade-leave-to {
        transform: translateX(100%);
    }
}
.download-popover{
    min-width: 120px !important;
    line-height: 35px;
    padding: 0;
    li{
        cursor: pointer;
        padding: 0 10px;
        &:hover{
            background: #f8f8f8;
        }
    }
}
</style>
