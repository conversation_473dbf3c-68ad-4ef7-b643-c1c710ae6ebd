<template>
    <ul class="hubble-chat__body-box" @scroll="$emit('loadMore', $event)">
        <li class="hubble-chat__body-empty" v-if="!messages.length">
            <img src="~img/empty.png" alt="">
            <p>在下方输入或在合同上选取后点击“Hubble”，进行提问</p>
        </li>
        <li v-for="(topic, index) in currentMessageList" :key="index" class="message">
            <div class="question" v-if="topic.question">
                <img src="~img/avatar.png" class="avatar" alt="">
                <div class="message-content">
                    <div class="quote chat-quote" v-if="topic.quote" @click="$emit('selectQuote', topic.questionDocumentQuote)">
                        {{ topic.quote }}
                    </div>
                    问题：{{ topic.question }}
                </div>
            </div>
            <span class="time">{{ topic.chatTime }}</span>
            <div class="answer" v-if="topic.answer">
                <img src="~img/AIAvatar.png" class="avatar" alt="">
                <div class="message-content">
                    {{ topic.answer }}
                    <span class="cursor" v-if="showCursor(index)"></span><br>
                    <span class="explain">~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记</span>
                </div>
            </div>
            <div class="related" v-show="topic.chatId">
                <template v-if="showAnswerDocumentQuotes(topic)">
                    相关内容：
                    <span v-for="(quote, i) in topic.answerDocumentQuotes" @click="$emit('selectQuote', quote)" :key="i">{{ quote.pageNumber }}</span>
                </template>
                <el-tooltip :open-delay="500" effect="dark" content="删除对话" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubbleshanchu" @click="handleDelete(index)"></i>
                </el-tooltip>
                <el-tooltip :open-delay="500" effect="dark" content="开启连续对话" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubblelianxuduihua" v-if="!isContinuousChat" @click="$emit('showContinuousChat', topic.chatId)"></i>
                </el-tooltip>
            </div>
        </li>
    </ul>
</template>

<script setup>
import { computed, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'

// Props
const props = defineProps({
    messages: {
        type: Array,
        default: () => [],
    },
    isContinuousChat: {
        type: Boolean,
        default: false,
    },
    typing: {
        type: Boolean,
        default: false,
    },
})

// Emits
const emit = defineEmits(['loadMore', 'selectQuote', 'showContinuousChat'])

const route = useRoute()

// Computed properties
const currentMessageList = computed(() => {
    return [...props.messages].reverse()
})

// Watchers
watch(() => props.messages, () => {
    nextTick(() => {
        scrollList()
    })
}, { deep: true, immediate: true })

// Methods
const showAnswerDocumentQuotes = (topic) => {
    return !!topic.answerDocumentQuotes?.length
}

const showCursor = (index) => {
    return index === 0 && props.typing
}

const scrollList = () => {
    const element = document.querySelector('.continuous-chat')?.querySelector('.hubble-chat__body-box')
    element && (element.scrollTop = element.scrollHeight)
}

const handleDelete = (index) => {
    ElMessageBox.confirm('确认删除该对话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
    }).then(() => {
        // 接口调用逻辑已移除，根据需求只保留回调部分
        // 原接口: this.$http.delete(`/web/hubble/topic/${route.params.topicId}/${currentMessageList.value[index].chatId}`)

        // 模拟删除成功后的回调
        ElMessage.success('删除成功')
        props.messages.splice(props.messages.length - index - 1, 1)
    }).catch(() => {})
}
</script>

<style lang="scss">
</style>
